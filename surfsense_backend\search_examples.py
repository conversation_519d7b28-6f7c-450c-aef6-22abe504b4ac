#!/usr/bin/env python3
"""
Enhanced Search Usage Examples
Demonstrates how to use the enhanced document hybrid search functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

async def example_basic_search():
    """Example 1: Basic enhanced hybrid search."""
    print("📝 Example 1: Basic Enhanced Hybrid Search")
    print("-" * 50)
    
    try:
        from app.db import get_async_session
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            
            # Basic search with default parameters
            results = await retriever.hybrid_search(
                query_text="machine learning tutorial",
                top_k=5,
                user_id="your-user-id",
                search_space_id=1
            )
            
            print(f"Found {len(results)} results:")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['title']}")
                print(f"   Score: {result.get('final_score', 0):.4f}")
                print(f"   Type: {result['document_type']}")
                print()
            
            break
            
    except Exception as e:
        print(f"Error: {e}")

async def example_advanced_search():
    """Example 2: Advanced search with custom parameters."""
    print("🔧 Example 2: Advanced Search with Custom Parameters")
    print("-" * 50)
    
    try:
        from app.db import get_async_session
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            
            # Advanced search with custom weights and filters
            results = await retriever.hybrid_search(
                query_text="python programming best practices",
                top_k=10,
                user_id="your-user-id",
                search_space_id=1,
                document_type="FILE",  # Only search files
                metadata_filters={"language": "en"},  # Only English content
                include_chunks=True,  # Include chunk content
                rerank=True,  # Apply reranking
                semantic_weight=0.7,  # Favor semantic search
                keyword_weight=0.3   # Less weight on keywords
            )
            
            print(f"Advanced search found {len(results)} results:")
            for result in results[:3]:
                print(f"• {result['title']}")
                print(f"  RRF Score: {result.get('rrf_score', 0):.4f}")
                print(f"  Final Score: {result.get('final_score', 0):.4f}")
                if 'rerank_score' in result:
                    print(f"  Rerank Score: {result['rerank_score']:.4f}")
                print(f"  Created: {result.get('created_at', 'Unknown')}")
                print()
            
            break
            
    except Exception as e:
        print(f"Error: {e}")

async def example_multimodal_search():
    """Example 3: Multi-modal search across document types."""
    print("🎯 Example 3: Multi-Modal Search")
    print("-" * 50)
    
    try:
        from app.db import get_async_session
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            
            # Search across multiple document types
            results = await retriever.multi_modal_search(
                query_text="API documentation",
                top_k=3,
                user_id="your-user-id",
                search_space_id=1,
                document_types=["FILE", "CRAWLED_URL", "NOTION_CONNECTOR"],
                metadata_filters={"category": "technical"}
            )
            
            print("Multi-modal search results by type:")
            for doc_type, type_results in results.items():
                print(f"\n{doc_type}: {len(type_results)} results")
                for result in type_results[:2]:
                    print(f"  • {result['title'][:60]}...")
                    print(f"    Score: {result.get('final_score', 0):.4f}")
            
            break
            
    except Exception as e:
        print(f"Error: {e}")

async def example_similarity_search():
    """Example 4: Pure semantic similarity search."""
    print("🧠 Example 4: Semantic Similarity Search")
    print("-" * 50)
    
    try:
        from app.db import get_async_session
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            
            # Pure semantic search with similarity threshold
            results = await retriever.semantic_similarity_search(
                query_text="neural networks deep learning",
                top_k=5,
                user_id="your-user-id",
                search_space_id=1,
                similarity_threshold=0.3  # Only return highly similar documents
            )
            
            print(f"Semantic similarity search found {len(results)} results:")
            for result in results:
                print(f"• {result['title']}")
                print(f"  Similarity: {result['similarity_score']:.4f}")
                print(f"  Type: {result['document_type']}")
                print()
            
            break
            
    except Exception as e:
        print(f"Error: {e}")

async def example_search_with_presets():
    """Example 5: Using search presets."""
    print("⚙️ Example 5: Search with Configuration Presets")
    print("-" * 50)
    
    try:
        from app.config.search_config import apply_search_preset, get_available_presets
        from app.db import get_async_session
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        
        # Show available presets
        presets = get_available_presets()
        print("Available search presets:")
        for name, preset in presets.items():
            print(f"  • {name}: {preset.description}")
        
        print("\nTesting 'semantic_focused' preset:")
        
        # Apply semantic-focused preset
        config = apply_search_preset('semantic_focused')
        print(f"Semantic weight: {config.semantic_weight}")
        print(f"Keyword weight: {config.keyword_weight}")
        print(f"Similarity threshold: {config.similarity_threshold}")
        
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            
            # Use preset parameters
            results = await retriever.hybrid_search(
                query_text="artificial intelligence concepts",
                top_k=5,
                user_id="your-user-id",
                search_space_id=1,
                **config.get_search_params()
            )
            
            print(f"\nSemantic-focused search found {len(results)} results")
            break
            
    except Exception as e:
        print(f"Error: {e}")

async def example_search_statistics():
    """Example 6: Getting search statistics."""
    print("📊 Example 6: Search Statistics")
    print("-" * 50)
    
    try:
        from app.db import get_async_session
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            
            # Get search statistics
            stats = await retriever.get_search_statistics(
                user_id="your-user-id",
                search_space_id=1
            )
            
            print("Search Statistics:")
            print(f"  Total Documents: {stats['total_documents']}")
            print(f"  Total Chunks: {stats['total_chunks']}")
            print(f"  Search Space ID: {stats['search_space_id']}")
            print(f"  Embedding Dimension: {stats['embedding_dimension']}")
            
            print("\nDocuments by Type:")
            for doc_type, count in stats['documents_by_type'].items():
                print(f"  {doc_type}: {count}")
            
            break
            
    except Exception as e:
        print(f"Error: {e}")

async def example_performance_comparison():
    """Example 7: Performance comparison between search methods."""
    print("⚡ Example 7: Performance Comparison")
    print("-" * 50)
    
    try:
        import time
        from app.db import get_async_session
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            query = "machine learning algorithms"
            user_id = "your-user-id"
            search_space_id = 1
            
            # Test vector search performance
            start_time = time.time()
            vector_results = await retriever.vector_search(query, 10, user_id, search_space_id)
            vector_time = time.time() - start_time
            
            # Test full-text search performance
            start_time = time.time()
            fts_results = await retriever.full_text_search(query, 10, user_id, search_space_id)
            fts_time = time.time() - start_time
            
            # Test hybrid search performance
            start_time = time.time()
            hybrid_results = await retriever.hybrid_search(query, 10, user_id, search_space_id, rerank=False)
            hybrid_time = time.time() - start_time
            
            # Test hybrid search with reranking
            start_time = time.time()
            hybrid_rerank_results = await retriever.hybrid_search(query, 10, user_id, search_space_id, rerank=True)
            hybrid_rerank_time = time.time() - start_time
            
            print("Performance Comparison:")
            print(f"  Vector Search: {len(vector_results)} results in {vector_time:.3f}s")
            print(f"  Full-Text Search: {len(fts_results)} results in {fts_time:.3f}s")
            print(f"  Hybrid Search: {len(hybrid_results)} results in {hybrid_time:.3f}s")
            print(f"  Hybrid + Rerank: {len(hybrid_rerank_results)} results in {hybrid_rerank_time:.3f}s")
            
            break
            
    except Exception as e:
        print(f"Error: {e}")

async def run_all_examples():
    """Run all search examples."""
    print("🚀 Enhanced Document Search Examples")
    print("=" * 60)
    
    examples = [
        example_basic_search,
        example_advanced_search,
        example_multimodal_search,
        example_similarity_search,
        example_search_with_presets,
        example_search_statistics,
        example_performance_comparison
    ]
    
    for i, example in enumerate(examples, 1):
        try:
            print(f"\n[{i}/{len(examples)}]")
            await example()
            print("\n" + "✅ Example completed successfully")
        except Exception as e:
            print(f"\n❌ Example failed: {e}")
        
        if i < len(examples):
            print("\n" + "─" * 60)
    
    print("\n" + "=" * 60)
    print("🎉 All examples completed!")

if __name__ == "__main__":
    print("Starting enhanced search examples...")
    asyncio.run(run_all_examples())
