#!/usr/bin/env python3
"""
Test script for the enhanced document hybrid search functionality.
This script demonstrates the new features and improvements.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

async def test_enhanced_search():
    """Test the enhanced search functionality."""
    
    print("🔍 Testing Enhanced Document Hybrid Search")
    print("=" * 50)
    
    try:
        # Import required modules
        from app.db import get_async_session, Document, SearchSpace, User
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        from sqlalchemy import select
        
        # Get database session
        async for session in get_async_session():
            retriever = DocumentHybridSearchRetriever(session)
            
            # Test 1: Basic functionality check
            print("\n1. Testing basic retriever initialization...")
            print(f"✅ Retriever initialized successfully")
            print(f"   - Cache size: {len(retriever._embedding_cache)}")
            print(f"   - Logger: {retriever.logger.name}")
            
            # Test 2: Query variations
            print("\n2. Testing query variations...")
            test_queries = [
                "machine learning algorithms",
                "Python programming tutorial",
                "database optimization techniques"
            ]
            
            for query in test_queries:
                variations = retriever._get_query_variations(query)
                print(f"   Query: '{query}'")
                print(f"   Variations: {variations}")
            
            # Test 3: Check if we have any users and documents
            print("\n3. Checking database content...")
            
            # Get first user
            user_result = await session.execute(select(User).limit(1))
            user = user_result.scalars().first()
            
            if not user:
                print("   ⚠️  No users found in database")
                return
            
            print(f"   Found user: {user.id}")
            
            # Get user's search spaces
            search_spaces_result = await session.execute(
                select(SearchSpace).where(SearchSpace.user_id == user.id).limit(1)
            )
            search_space = search_spaces_result.scalars().first()
            
            if not search_space:
                print("   ⚠️  No search spaces found for user")
                return
                
            print(f"   Found search space: {search_space.id} - {search_space.name}")
            
            # Get documents count
            docs_result = await session.execute(
                select(Document).where(Document.search_space_id == search_space.id)
            )
            documents = docs_result.scalars().all()
            
            print(f"   Found {len(documents)} documents in search space")
            
            if len(documents) == 0:
                print("   ⚠️  No documents found for testing")
                return
            
            # Test 4: Search statistics
            print("\n4. Testing search statistics...")
            try:
                stats = await retriever.get_search_statistics(
                    user_id=str(user.id),
                    search_space_id=search_space.id
                )
                print(f"   📊 Search Statistics:")
                print(f"      - Total documents: {stats['total_documents']}")
                print(f"      - Total chunks: {stats['total_chunks']}")
                print(f"      - Documents by type: {stats['documents_by_type']}")
            except Exception as e:
                print(f"   ❌ Error getting statistics: {e}")
            
            # Test 5: Enhanced vector search
            print("\n5. Testing enhanced vector search...")
            try:
                test_query = "programming tutorial"
                vector_results = await retriever.vector_search(
                    query_text=test_query,
                    top_k=3,
                    user_id=str(user.id),
                    search_space_id=search_space.id,
                    metadata_filters=None
                )
                print(f"   Found {len(vector_results)} vector search results")
                for i, doc in enumerate(vector_results[:2]):
                    print(f"      {i+1}. {doc.title[:50]}...")
            except Exception as e:
                print(f"   ❌ Error in vector search: {e}")
            
            # Test 6: Enhanced full-text search
            print("\n6. Testing enhanced full-text search...")
            try:
                fts_results = await retriever.full_text_search(
                    query_text=test_query,
                    top_k=3,
                    user_id=str(user.id),
                    search_space_id=search_space.id,
                    boost_recent=True
                )
                print(f"   Found {len(fts_results)} full-text search results")
                for i, doc in enumerate(fts_results[:2]):
                    print(f"      {i+1}. {doc.title[:50]}...")
            except Exception as e:
                print(f"   ❌ Error in full-text search: {e}")
            
            # Test 7: Enhanced hybrid search
            print("\n7. Testing enhanced hybrid search...")
            try:
                hybrid_results = await retriever.hybrid_search(
                    query_text=test_query,
                    top_k=5,
                    user_id=str(user.id),
                    search_space_id=search_space.id,
                    include_chunks=True,
                    rerank=True,
                    semantic_weight=0.6,
                    keyword_weight=0.4
                )
                print(f"   Found {len(hybrid_results)} hybrid search results")
                for i, result in enumerate(hybrid_results[:3]):
                    print(f"      {i+1}. {result['title'][:50]}...")
                    print(f"          RRF Score: {result.get('rrf_score', 'N/A'):.4f}")
                    print(f"          Final Score: {result.get('final_score', 'N/A'):.4f}")
                    if 'rerank_score' in result:
                        print(f"          Rerank Score: {result['rerank_score']:.4f}")
            except Exception as e:
                print(f"   ❌ Error in hybrid search: {e}")
            
            # Test 8: Multi-modal search
            print("\n8. Testing multi-modal search...")
            try:
                multimodal_results = await retriever.multi_modal_search(
                    query_text=test_query,
                    top_k=2,
                    user_id=str(user.id),
                    search_space_id=search_space.id,
                    document_types=["FILE", "CRAWLED_URL", "EXTENSION"]
                )
                print(f"   Multi-modal search results:")
                for doc_type, results in multimodal_results.items():
                    print(f"      {doc_type}: {len(results)} results")
            except Exception as e:
                print(f"   ❌ Error in multi-modal search: {e}")
            
            # Test 9: Semantic similarity search
            print("\n9. Testing semantic similarity search...")
            try:
                similarity_results = await retriever.semantic_similarity_search(
                    query_text=test_query,
                    top_k=3,
                    user_id=str(user.id),
                    search_space_id=search_space.id,
                    similarity_threshold=0.1  # Lower threshold for testing
                )
                print(f"   Found {len(similarity_results)} similarity results")
                for i, result in enumerate(similarity_results[:2]):
                    print(f"      {i+1}. {result['title'][:50]}...")
                    print(f"          Similarity: {result['similarity_score']:.4f}")
            except Exception as e:
                print(f"   ❌ Error in similarity search: {e}")
            
            print("\n" + "=" * 50)
            print("✅ Enhanced search testing completed!")
            
            break  # Exit the async generator
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting enhanced search tests...")
    asyncio.run(test_enhanced_search())
