import logging
from typing import List, Dict, Any, Optional, Union
from functools import lru_cache
import asyncio

class DocumentHybridSearchRetriever:
    def __init__(self, db_session):
        """
        Initialize the hybrid search retriever with a database session.

        Args:
            db_session: SQLAlchemy AsyncSession from FastAPI dependency injection
        """
        self.db_session = db_session
        self._embedding_cache = {}
        self.logger = logging.getLogger(__name__)

    @lru_cache(maxsize=128)
    def _get_query_variations(self, query_text: str) -> List[str]:
        """
        Generate query variations for better search coverage.

        Args:
            query_text: Original query text

        Returns:
            List of query variations
        """
        variations = [query_text]

        # Add stemmed/simplified versions
        words = query_text.lower().split()
        if len(words) > 1:
            # Add individual important words
            important_words = [w for w in words if len(w) > 3]
            if important_words:
                variations.append(" ".join(important_words))

        return variations

    async def _get_cached_embedding(self, query_text: str):
        """
        Get cached embedding or compute new one.

        Args:
            query_text: Text to embed

        Returns:
            Embedding vector
        """
        if query_text in self._embedding_cache:
            return self._embedding_cache[query_text]

        from app.config import config
        embedding_model = config.embedding_model_instance
        embedding = embedding_model.embed(query_text)

        # Cache with size limit
        if len(self._embedding_cache) > 100:
            # Remove oldest entry
            oldest_key = next(iter(self._embedding_cache))
            del self._embedding_cache[oldest_key]

        self._embedding_cache[query_text] = embedding
        return embedding

    async def vector_search(self, query_text: str, top_k: int, user_id: str, search_space_id: int = None,
                          metadata_filters: Dict[str, Any] = None) -> list:
        """
        Perform vector similarity search on documents with enhanced filtering.

        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            metadata_filters: Optional metadata filters

        Returns:
            List of documents sorted by vector similarity
        """
        from sqlalchemy import select, func, and_
        from sqlalchemy.orm import joinedload
        from app.db import Document, SearchSpace

        # Get cached embedding for the query
        query_embedding = await self._get_cached_embedding(query_text)

        # Build the base query with user ownership check
        query = (
            select(Document)
            .options(joinedload(Document.search_space))
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
        )

        # Add search space filter if provided
        if search_space_id is not None:
            query = query.where(Document.search_space_id == search_space_id)

        # Add metadata filters if provided
        if metadata_filters:
            for key, value in metadata_filters.items():
                query = query.where(Document.document_metadata[key].astext == str(value))

        # Add vector similarity ordering with distance threshold
        query = (
            query
            .order_by(Document.embedding.op("<=>")(query_embedding))
            .limit(top_k * 2)  # Get more results for potential filtering
        )

        # Execute the query
        result = await self.db_session.execute(query)
        documents = result.scalars().all()

        # Filter by similarity threshold if needed (optional enhancement)
        # This could be made configurable
        return documents[:top_k]

    async def full_text_search(self, query_text: str, top_k: int, user_id: str, search_space_id: int = None,
                             metadata_filters: Dict[str, Any] = None, boost_recent: bool = True) -> list:
        """
        Perform enhanced full-text keyword search on documents.

        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            metadata_filters: Optional metadata filters
            boost_recent: Whether to boost recent documents

        Returns:
            List of documents sorted by text relevance
        """
        from sqlalchemy import select, func, text, and_, case
        from sqlalchemy.orm import joinedload
        from app.db import Document, SearchSpace
        from datetime import datetime, timedelta

        # Create enhanced tsvector including title with higher weight
        tsvector = func.to_tsvector('english',
            func.coalesce(Document.title, '') + ' ' + func.coalesce(Document.content, ''))

        # Try different query types for better matching
        query_variations = self._get_query_variations(query_text)
        tsquery_conditions = []

        for variation in query_variations:
            # Use both plainto_tsquery and phraseto_tsquery for better coverage
            plainto_query = func.plainto_tsquery('english', variation)
            phrase_query = func.phraseto_tsquery('english', variation)
            tsquery_conditions.extend([
                tsvector.op("@@")(plainto_query),
                tsvector.op("@@")(phrase_query)
            ])

        # Combine query conditions with OR
        tsquery_condition = tsquery_conditions[0]
        for condition in tsquery_conditions[1:]:
            tsquery_condition = tsquery_condition | condition

        # Build the base query with user ownership check
        query = (
            select(Document)
            .options(joinedload(Document.search_space))
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
            .where(tsquery_condition)  # Only include results that match the query
        )

        # Add search space filter if provided
        if search_space_id is not None:
            query = query.where(Document.search_space_id == search_space_id)

        # Add metadata filters if provided
        if metadata_filters:
            for key, value in metadata_filters.items():
                query = query.where(Document.document_metadata[key].astext == str(value))

        # Enhanced ranking with recency boost
        base_tsquery = func.plainto_tsquery('english', query_text)
        base_rank = func.ts_rank_cd(tsvector, base_tsquery)

        if boost_recent:
            # Boost documents created in the last 30 days
            recent_threshold = datetime.utcnow() - timedelta(days=30)
            recency_boost = case(
                (Document.created_at >= recent_threshold, 1.2),
                else_=1.0
            )
            final_rank = base_rank * recency_boost
        else:
            final_rank = base_rank

        # Add text search ranking
        query = (
            query
            .order_by(final_rank.desc())
            .limit(top_k)
        )

        # Execute the query
        result = await self.db_session.execute(query)
        documents = result.scalars().all()

        return documents

    async def _get_reranker_service(self):
        """Get reranker service instance."""
        try:
            from app.utils.reranker_service import RerankerService
            from app.config import config
            return RerankerService.get_reranker_instance(config)
        except Exception as e:
            self.logger.warning(f"Could not initialize reranker service: {e}")
            return None

    async def hybrid_search(self, query_text: str, top_k: int, user_id: str, search_space_id: int = None,
                          document_type: str = None, metadata_filters: Dict[str, Any] = None,
                          include_chunks: bool = True, rerank: bool = True,
                          semantic_weight: float = 0.6, keyword_weight: float = 0.4) -> list:
        """
        Enhanced hybrid search combining vector similarity and full-text search with advanced features.

        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            document_type: Optional document type to filter results (e.g., "FILE", "CRAWLED_URL")
            metadata_filters: Optional metadata filters
            include_chunks: Whether to include chunk-level search
            rerank: Whether to apply reranking
            semantic_weight: Weight for semantic search results (0.0-1.0)
            keyword_weight: Weight for keyword search results (0.0-1.0)

        Returns:
            List of enhanced search results with scores and metadata
        """
        from sqlalchemy import select, func, text, and_, case
        from sqlalchemy.orm import joinedload
        from app.db import Document, SearchSpace, DocumentType, Chunk
        from app.config import config
        from datetime import datetime, timedelta

        # Validate weights
        if abs(semantic_weight + keyword_weight - 1.0) > 0.01:
            self.logger.warning("Semantic and keyword weights should sum to 1.0, normalizing...")
            total_weight = semantic_weight + keyword_weight
            semantic_weight = semantic_weight / total_weight
            keyword_weight = keyword_weight / total_weight

        # Get cached embedding for the query
        query_embedding = await self._get_cached_embedding(query_text)

        # Enhanced constants for RRF calculation
        k = 60  # Constant for RRF calculation
        n_results = min(top_k * 3, 100)  # Get more results for better fusion, but cap it
        
        # Enhanced tsvector including title with higher weight
        tsvector = func.to_tsvector('english',
            func.coalesce(Document.title, '') + ' ' + func.coalesce(Document.content, ''))

        # Create multiple query variations for better matching
        query_variations = self._get_query_variations(query_text)
        tsquery_list = []
        for variation in query_variations:
            tsquery_list.append(func.plainto_tsquery('english', variation))

        # Use the primary query for ranking
        primary_tsquery = tsquery_list[0]

        # Base conditions for document filtering
        base_conditions = [SearchSpace.user_id == user_id]

        # Add search space filter if provided
        if search_space_id is not None:
            base_conditions.append(Document.search_space_id == search_space_id)

        # Add document type filter if provided
        if document_type is not None:
            # Convert string to enum value if needed
            if isinstance(document_type, str):
                try:
                    doc_type_enum = DocumentType[document_type]
                    base_conditions.append(Document.document_type == doc_type_enum)
                except KeyError:
                    # If the document type doesn't exist in the enum, return empty results
                    return []
            else:
                base_conditions.append(Document.document_type == document_type)

        # Add metadata filters if provided
        if metadata_filters:
            for key, value in metadata_filters.items():
                base_conditions.append(Document.document_metadata[key].astext == str(value))
        
        # Enhanced CTE for semantic search with distance scoring
        semantic_search_cte = (
            select(
                Document.id,
                func.rank().over(order_by=Document.embedding.op("<=>")(query_embedding)).label("rank"),
                Document.embedding.op("<=>")(query_embedding).label("distance")
            )
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(and_(*base_conditions))
        )

        semantic_search_cte = (
            semantic_search_cte
            .order_by(Document.embedding.op("<=>")(query_embedding))
            .limit(n_results)
            .cte("semantic_search")
        )
        
        # Enhanced CTE for keyword search with multiple query matching
        # Create OR condition for multiple query variations
        tsquery_conditions = [tsvector.op("@@")(tsq) for tsq in tsquery_list]
        combined_tsquery_condition = tsquery_conditions[0]
        for condition in tsquery_conditions[1:]:
            combined_tsquery_condition = combined_tsquery_condition | condition

        # Enhanced ranking with recency boost
        recent_threshold = datetime.utcnow() - timedelta(days=30)
        recency_boost = case(
            (Document.created_at >= recent_threshold, 1.2),
            else_=1.0
        )
        enhanced_rank = func.ts_rank_cd(tsvector, primary_tsquery) * recency_boost

        keyword_search_cte = (
            select(
                Document.id,
                func.rank().over(order_by=enhanced_rank.desc()).label("rank"),
                enhanced_rank.label("ts_rank")
            )
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(and_(*base_conditions))
            .where(combined_tsquery_condition)
        )

        keyword_search_cte = (
            keyword_search_cte
            .order_by(enhanced_rank.desc())
            .limit(n_results)
            .cte("keyword_search")
        )
        
        # Enhanced RRF scoring with weighted combination
        final_query = (
            select(
                Document,
                (
                    func.coalesce(semantic_weight / (k + semantic_search_cte.c.rank), 0.0) +
                    func.coalesce(keyword_weight / (k + keyword_search_cte.c.rank), 0.0)
                ).label("rrf_score"),
                semantic_search_cte.c.distance.label("semantic_distance"),
                keyword_search_cte.c.ts_rank.label("keyword_rank")
            )
            .select_from(
                semantic_search_cte.outerjoin(
                    keyword_search_cte,
                    semantic_search_cte.c.id == keyword_search_cte.c.id,
                    full=True
                )
            )
            .join(
                Document,
                Document.id == func.coalesce(semantic_search_cte.c.id, keyword_search_cte.c.id)
            )
            .options(joinedload(Document.search_space))
            .order_by(text("rrf_score DESC"))
            .limit(min(top_k * 2, 50))  # Get more for potential reranking
        )
        
        # Execute the query
        result = await self.db_session.execute(final_query)
        documents_with_scores = result.all()

        # If no results were found, return an empty list
        if not documents_with_scores:
            return []

        self.logger.info(f"Found {len(documents_with_scores)} documents before processing")
        
        # Prepare initial results
        initial_results = []
        for row in documents_with_scores:
            document = row[0]
            rrf_score = row[1]
            semantic_distance = row[2] if len(row) > 2 else None
            keyword_rank = row[3] if len(row) > 3 else None

            # Fetch associated chunks if requested
            chunks_content = ""
            if include_chunks:
                try:
                    from sqlalchemy import select
                    chunks_query = select(Chunk).where(Chunk.document_id == document.id).order_by(Chunk.id)
                    chunks_result = await self.db_session.execute(chunks_query)
                    chunks = chunks_result.scalars().all()
                    chunks_content = " ".join([chunk.content for chunk in chunks]) if chunks else ""
                except Exception as e:
                    self.logger.warning(f"Error fetching chunks for document {document.id}: {e}")
                    chunks_content = ""

            result_item = {
                "document_id": document.id,
                "title": document.title,
                "content": document.content,
                "chunks_content": chunks_content,
                "document_type": document.document_type.value if hasattr(document, 'document_type') else None,
                "metadata": document.document_metadata,
                "rrf_score": float(rrf_score),
                "semantic_distance": float(semantic_distance) if semantic_distance is not None else None,
                "keyword_rank": float(keyword_rank) if keyword_rank is not None else None,
                "search_space_id": document.search_space_id,
                "created_at": document.created_at.isoformat() if hasattr(document, 'created_at') else None
            }
            initial_results.append(result_item)

        # Apply reranking if requested and available
        final_results = initial_results
        if rerank and len(initial_results) > 1:
            try:
                reranker_service = await self._get_reranker_service()
                if reranker_service:
                    # Convert to format expected by reranker
                    reranker_docs = []
                    for i, result in enumerate(initial_results):
                        content_for_reranking = result["content"]
                        if include_chunks and result["chunks_content"]:
                            content_for_reranking = result["chunks_content"]

                        reranker_docs.append({
                            "chunk_id": f"doc_{result['document_id']}",
                            "content": content_for_reranking,
                            "score": result["rrf_score"],
                            "document": {
                                "id": result["document_id"],
                                "title": result["title"],
                                "document_type": result["document_type"],
                                "metadata": result["metadata"]
                            }
                        })

                    # Rerank the documents
                    reranked_docs = reranker_service.rerank_documents(query_text, reranker_docs)

                    # Map back to original format
                    reranked_results = []
                    for reranked_doc in reranked_docs:
                        doc_id = reranked_doc["document"]["id"]
                        original_result = next((r for r in initial_results if r["document_id"] == doc_id), None)
                        if original_result:
                            original_result["rerank_score"] = reranked_doc["score"]
                            original_result["final_score"] = reranked_doc["score"]  # Use rerank score as final
                            reranked_results.append(original_result)

                    final_results = reranked_results
                    self.logger.info(f"Reranked {len(final_results)} documents")
                else:
                    # No reranker available, use RRF score as final score
                    for result in final_results:
                        result["final_score"] = result["rrf_score"]
            except Exception as e:
                self.logger.error(f"Error during reranking: {e}")
                # Fallback to RRF scores
                for result in final_results:
                    result["final_score"] = result["rrf_score"]
        else:
            # No reranking requested, use RRF score as final score
            for result in final_results:
                result["final_score"] = result["rrf_score"]

        # Sort by final score and limit to requested number
        final_results.sort(key=lambda x: x["final_score"], reverse=True)
        final_results = final_results[:top_k]

        self.logger.info(f"Returning {len(final_results)} final results")
        return final_results

    async def multi_modal_search(self, query_text: str, top_k: int, user_id: str,
                                search_space_id: int = None, document_types: List[str] = None,
                                metadata_filters: Dict[str, Any] = None) -> Dict[str, List]:
        """
        Perform search across multiple document types and return categorized results.

        Args:
            query_text: The search query text
            top_k: Number of results to return per category
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            document_types: List of document types to search
            metadata_filters: Optional metadata filters

        Returns:
            Dictionary with categorized search results
        """
        if not document_types:
            document_types = ["FILE", "CRAWLED_URL", "EXTENSION", "SLACK_CONNECTOR", "NOTION_CONNECTOR"]

        results = {}

        # Search each document type separately
        for doc_type in document_types:
            try:
                type_results = await self.hybrid_search(
                    query_text=query_text,
                    top_k=top_k,
                    user_id=user_id,
                    search_space_id=search_space_id,
                    document_type=doc_type,
                    metadata_filters=metadata_filters,
                    rerank=True
                )
                results[doc_type] = type_results
            except Exception as e:
                self.logger.error(f"Error searching document type {doc_type}: {e}")
                results[doc_type] = []

        return results

    async def semantic_similarity_search(self, query_text: str, top_k: int, user_id: str,
                                       search_space_id: int = None, similarity_threshold: float = 0.7) -> List:
        """
        Perform pure semantic similarity search with threshold filtering.

        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            similarity_threshold: Minimum similarity threshold (0.0-1.0)

        Returns:
            List of documents with similarity scores
        """
        from sqlalchemy import select, func
        from sqlalchemy.orm import joinedload
        from app.db import Document, SearchSpace

        # Get cached embedding for the query
        query_embedding = await self._get_cached_embedding(query_text)

        # Build query with similarity threshold
        query = (
            select(
                Document,
                (1 - Document.embedding.op("<=>")(query_embedding)).label("similarity")
            )
            .options(joinedload(Document.search_space))
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
            .where((1 - Document.embedding.op("<=>")(query_embedding)) >= similarity_threshold)
        )

        if search_space_id is not None:
            query = query.where(Document.search_space_id == search_space_id)

        query = query.order_by(Document.embedding.op("<=>")(query_embedding)).limit(top_k)

        result = await self.db_session.execute(query)
        documents_with_similarity = result.all()

        # Format results
        results = []
        for document, similarity in documents_with_similarity:
            results.append({
                "document_id": document.id,
                "title": document.title,
                "content": document.content,
                "document_type": document.document_type.value if hasattr(document, 'document_type') else None,
                "metadata": document.document_metadata,
                "similarity_score": float(similarity),
                "search_space_id": document.search_space_id
            })

        return results

    async def get_search_statistics(self, user_id: str, search_space_id: int = None) -> Dict[str, Any]:
        """
        Get search statistics for the user's documents.

        Args:
            user_id: The ID of the user
            search_space_id: Optional search space ID to filter results

        Returns:
            Dictionary with search statistics
        """
        from sqlalchemy import select, func, distinct
        from app.db import Document, SearchSpace, Chunk

        # Base query
        base_query = (
            select(Document)
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
        )

        if search_space_id is not None:
            base_query = base_query.where(Document.search_space_id == search_space_id)

        # Get document count by type
        doc_type_query = (
            select(Document.document_type, func.count(Document.id))
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
            .group_by(Document.document_type)
        )

        if search_space_id is not None:
            doc_type_query = doc_type_query.where(Document.search_space_id == search_space_id)

        # Execute queries
        total_docs_result = await self.db_session.execute(
            select(func.count(Document.id)).select_from(base_query.subquery())
        )
        total_docs = total_docs_result.scalar()

        doc_types_result = await self.db_session.execute(doc_type_query)
        doc_types = {doc_type.value: count for doc_type, count in doc_types_result.all()}

        # Get chunk statistics
        chunk_query = (
            select(func.count(Chunk.id))
            .join(Document, Chunk.document_id == Document.id)
            .join(SearchSpace, Document.search_space_id == SearchSpace.id)
            .where(SearchSpace.user_id == user_id)
        )

        if search_space_id is not None:
            chunk_query = chunk_query.where(Document.search_space_id == search_space_id)

        total_chunks_result = await self.db_session.execute(chunk_query)
        total_chunks = total_chunks_result.scalar()

        return {
            "total_documents": total_docs,
            "total_chunks": total_chunks,
            "documents_by_type": doc_types,
            "search_space_id": search_space_id,
            "embedding_dimension": getattr(self, '_embedding_dimension', 'unknown')
        }