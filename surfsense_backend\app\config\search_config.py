"""
Enhanced Search Configuration
Centralized configuration for the enhanced document hybrid search system.
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from enum import Enum

class SearchMode(Enum):
    """Search mode enumeration."""
    HYBRID = "hybrid"
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    MULTIMODAL = "multimodal"

class RankingStrategy(Enum):
    """Ranking strategy enumeration."""
    RRF = "reciprocal_rank_fusion"
    WEIGHTED = "weighted_combination"
    RERANKER = "reranker_only"
    HYBRID_RERANK = "hybrid_with_rerank"

@dataclass
class SearchConfig:
    """Configuration class for enhanced search functionality."""
    
    # Core Search Parameters
    default_top_k: int = 10
    max_top_k: int = 100
    default_search_mode: SearchMode = SearchMode.HYBRID
    
    # Hybrid Search Weights
    semantic_weight: float = 0.6
    keyword_weight: float = 0.4
    
    # RRF Parameters
    rrf_k_constant: int = 60
    rrf_multiplier: int = 2  # Multiplier for n_results = top_k * multiplier
    
    # Caching Configuration
    embedding_cache_size: int = 100
    query_cache_ttl: int = 3600  # seconds
    enable_caching: bool = True
    
    # Reranking Configuration
    enable_reranking: bool = True
    rerank_top_k_multiplier: int = 2
    rerank_fallback_to_rrf: bool = True
    
    # Full-Text Search Configuration
    enable_recency_boost: bool = True
    recency_boost_days: int = 30
    recency_boost_factor: float = 1.2
    enable_title_boost: bool = True
    title_boost_factor: float = 1.5
    
    # Vector Search Configuration
    similarity_threshold: float = 0.1
    enable_similarity_filtering: bool = False
    vector_search_multiplier: int = 2
    
    # Query Processing
    enable_query_expansion: bool = True
    min_word_length_for_expansion: int = 3
    max_query_variations: int = 3
    
    # Performance Configuration
    enable_parallel_search: bool = True
    max_concurrent_searches: int = 4
    search_timeout_seconds: int = 30
    
    # Logging Configuration
    enable_search_logging: bool = True
    log_slow_queries: bool = True
    slow_query_threshold_ms: int = 1000
    
    # Document Type Priorities
    document_type_weights: Dict[str, float] = None
    
    # Metadata Filtering
    enable_metadata_filtering: bool = True
    max_metadata_filters: int = 10
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        # Validate weights sum to 1.0
        if abs(self.semantic_weight + self.keyword_weight - 1.0) > 0.01:
            total = self.semantic_weight + self.keyword_weight
            self.semantic_weight = self.semantic_weight / total
            self.keyword_weight = self.keyword_weight / total
        
        # Set default document type weights if not provided
        if self.document_type_weights is None:
            self.document_type_weights = {
                "FILE": 1.0,
                "CRAWLED_URL": 0.9,
                "EXTENSION": 0.8,
                "SLACK_CONNECTOR": 0.9,
                "NOTION_CONNECTOR": 0.95,
                "YOUTUBE_VIDEO": 0.7,
                "GITHUB_CONNECTOR": 0.85,
                "LINEAR_CONNECTOR": 0.8,
                "DISCORD_CONNECTOR": 0.7
            }
    
    @classmethod
    def from_env(cls) -> 'SearchConfig':
        """Create configuration from environment variables."""
        return cls(
            default_top_k=int(os.getenv('SEARCH_DEFAULT_TOP_K', '10')),
            max_top_k=int(os.getenv('SEARCH_MAX_TOP_K', '100')),
            semantic_weight=float(os.getenv('SEARCH_SEMANTIC_WEIGHT', '0.6')),
            keyword_weight=float(os.getenv('SEARCH_KEYWORD_WEIGHT', '0.4')),
            rrf_k_constant=int(os.getenv('SEARCH_RRF_K', '60')),
            embedding_cache_size=int(os.getenv('SEARCH_CACHE_SIZE', '100')),
            enable_reranking=os.getenv('SEARCH_ENABLE_RERANKING', 'true').lower() == 'true',
            enable_recency_boost=os.getenv('SEARCH_ENABLE_RECENCY_BOOST', 'true').lower() == 'true',
            recency_boost_days=int(os.getenv('SEARCH_RECENCY_DAYS', '30')),
            recency_boost_factor=float(os.getenv('SEARCH_RECENCY_FACTOR', '1.2')),
            similarity_threshold=float(os.getenv('SEARCH_SIMILARITY_THRESHOLD', '0.1')),
            enable_query_expansion=os.getenv('SEARCH_ENABLE_QUERY_EXPANSION', 'true').lower() == 'true',
            enable_search_logging=os.getenv('SEARCH_ENABLE_LOGGING', 'true').lower() == 'true',
            search_timeout_seconds=int(os.getenv('SEARCH_TIMEOUT_SECONDS', '30')),
        )
    
    def get_search_params(self, **overrides) -> Dict[str, Any]:
        """Get search parameters with optional overrides."""
        params = {
            'semantic_weight': self.semantic_weight,
            'keyword_weight': self.keyword_weight,
            'include_chunks': True,
            'rerank': self.enable_reranking,
            'boost_recent': self.enable_recency_boost,
        }
        params.update(overrides)
        return params
    
    def get_rrf_params(self) -> Dict[str, Any]:
        """Get RRF-specific parameters."""
        return {
            'k': self.rrf_k_constant,
            'n_results_multiplier': self.rrf_multiplier
        }
    
    def get_cache_config(self) -> Dict[str, Any]:
        """Get caching configuration."""
        return {
            'enabled': self.enable_caching,
            'size': self.embedding_cache_size,
            'ttl': self.query_cache_ttl
        }
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance-related configuration."""
        return {
            'parallel_search': self.enable_parallel_search,
            'max_concurrent': self.max_concurrent_searches,
            'timeout': self.search_timeout_seconds,
            'log_slow_queries': self.log_slow_queries,
            'slow_threshold_ms': self.slow_query_threshold_ms
        }

@dataclass
class SearchPreset:
    """Predefined search configuration presets."""
    name: str
    description: str
    config_overrides: Dict[str, Any]

# Predefined search presets
SEARCH_PRESETS = {
    'balanced': SearchPreset(
        name='Balanced Search',
        description='Balanced semantic and keyword search with reranking',
        config_overrides={
            'semantic_weight': 0.6,
            'keyword_weight': 0.4,
            'enable_reranking': True,
            'enable_recency_boost': True
        }
    ),
    
    'semantic_focused': SearchPreset(
        name='Semantic Focused',
        description='Prioritize semantic similarity over keyword matching',
        config_overrides={
            'semantic_weight': 0.8,
            'keyword_weight': 0.2,
            'enable_reranking': True,
            'similarity_threshold': 0.3
        }
    ),
    
    'keyword_focused': SearchPreset(
        name='Keyword Focused',
        description='Prioritize exact keyword matching',
        config_overrides={
            'semantic_weight': 0.3,
            'keyword_weight': 0.7,
            'enable_recency_boost': True,
            'enable_title_boost': True
        }
    ),
    
    'recent_documents': SearchPreset(
        name='Recent Documents',
        description='Boost recently created documents',
        config_overrides={
            'enable_recency_boost': True,
            'recency_boost_days': 7,
            'recency_boost_factor': 2.0,
            'semantic_weight': 0.5,
            'keyword_weight': 0.5
        }
    ),
    
    'high_precision': SearchPreset(
        name='High Precision',
        description='Optimize for precision over recall',
        config_overrides={
            'enable_reranking': True,
            'similarity_threshold': 0.5,
            'enable_similarity_filtering': True,
            'rrf_k_constant': 30
        }
    ),
    
    'high_recall': SearchPreset(
        name='High Recall',
        description='Optimize for recall over precision',
        config_overrides={
            'enable_query_expansion': True,
            'max_query_variations': 5,
            'similarity_threshold': 0.1,
            'rrf_multiplier': 3
        }
    ),
    
    'fast_search': SearchPreset(
        name='Fast Search',
        description='Optimize for speed over accuracy',
        config_overrides={
            'enable_reranking': False,
            'enable_query_expansion': False,
            'rrf_multiplier': 1,
            'enable_parallel_search': True
        }
    )
}

# Global search configuration instance
search_config = SearchConfig.from_env()

def get_search_config() -> SearchConfig:
    """Get the global search configuration."""
    return search_config

def apply_search_preset(preset_name: str) -> SearchConfig:
    """Apply a search preset to the global configuration."""
    if preset_name not in SEARCH_PRESETS:
        raise ValueError(f"Unknown preset: {preset_name}. Available: {list(SEARCH_PRESETS.keys())}")
    
    preset = SEARCH_PRESETS[preset_name]
    config = SearchConfig.from_env()
    
    # Apply preset overrides
    for key, value in preset.config_overrides.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    return config

def get_available_presets() -> Dict[str, SearchPreset]:
    """Get all available search presets."""
    return SEARCH_PRESETS.copy()

# Environment variable documentation
ENV_VARS_DOCS = """
Enhanced Search Environment Variables:

# Core Search Configuration
SEARCH_DEFAULT_TOP_K=10                    # Default number of results to return
SEARCH_MAX_TOP_K=100                       # Maximum allowed top_k value
SEARCH_SEMANTIC_WEIGHT=0.6                 # Weight for semantic search (0.0-1.0)
SEARCH_KEYWORD_WEIGHT=0.4                  # Weight for keyword search (0.0-1.0)

# RRF Configuration
SEARCH_RRF_K=60                           # RRF k constant for ranking fusion

# Caching Configuration
SEARCH_CACHE_SIZE=100                     # Embedding cache size
SEARCH_ENABLE_CACHING=true                # Enable/disable caching

# Reranking Configuration
SEARCH_ENABLE_RERANKING=true              # Enable/disable reranking
SEARCH_RERANK_FALLBACK=true               # Fallback to RRF if reranking fails

# Full-Text Search Configuration
SEARCH_ENABLE_RECENCY_BOOST=true          # Boost recent documents
SEARCH_RECENCY_DAYS=30                    # Days to consider as "recent"
SEARCH_RECENCY_FACTOR=1.2                 # Boost factor for recent docs

# Vector Search Configuration
SEARCH_SIMILARITY_THRESHOLD=0.1           # Minimum similarity threshold
SEARCH_ENABLE_SIMILARITY_FILTERING=false  # Enable similarity filtering

# Query Processing
SEARCH_ENABLE_QUERY_EXPANSION=true        # Enable query expansion
SEARCH_MIN_WORD_LENGTH=3                  # Minimum word length for expansion

# Performance Configuration
SEARCH_TIMEOUT_SECONDS=30                 # Search timeout in seconds
SEARCH_ENABLE_LOGGING=true                # Enable search logging
SEARCH_LOG_SLOW_QUERIES=true              # Log slow queries
SEARCH_SLOW_THRESHOLD_MS=1000             # Slow query threshold in milliseconds
"""
