#!/usr/bin/env python3
"""
Database index optimization script for enhanced search performance.
This script creates additional indexes to support the enhanced search functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

async def optimize_search_indexes():
    """Create optimized indexes for enhanced search performance."""
    
    print("🔧 Optimizing Database Indexes for Enhanced Search")
    print("=" * 60)
    
    try:
        from app.db import engine
        from sqlalchemy import text
        
        async with engine.begin() as conn:
            print("\n1. Creating enhanced vector indexes...")
            
            # Enhanced HNSW indexes with better parameters
            indexes_to_create = [
                # Document vector indexes with optimized parameters
                """
                CREATE INDEX IF NOT EXISTS idx_documents_embedding_hnsw_optimized 
                ON documents USING hnsw (embedding public.vector_cosine_ops) 
                WITH (m = 16, ef_construction = 64)
                """,
                
                # Chunk vector indexes with optimized parameters  
                """
                CREATE INDEX IF NOT EXISTS idx_chunks_embedding_hnsw_optimized 
                ON chunks USING hnsw (embedding public.vector_cosine_ops) 
                WITH (m = 16, ef_construction = 64)
                """,
                
                # Enhanced GIN indexes for full-text search
                """
                CREATE INDEX IF NOT EXISTS idx_documents_content_title_gin 
                ON documents USING gin (
                    to_tsvector('english', coalesce(title, '') || ' ' || coalesce(content, ''))
                )
                """,
                
                """
                CREATE INDEX IF NOT EXISTS idx_chunks_content_gin_optimized 
                ON chunks USING gin (to_tsvector('english', content))
                """,
                
                # Composite indexes for filtering
                """
                CREATE INDEX IF NOT EXISTS idx_documents_search_space_type 
                ON documents (search_space_id, document_type)
                """,
                
                """
                CREATE INDEX IF NOT EXISTS idx_documents_created_at_desc 
                ON documents (created_at DESC)
                """,
                
                # Metadata indexes using GIN for JSON
                """
                CREATE INDEX IF NOT EXISTS idx_documents_metadata_gin 
                ON documents USING gin (document_metadata)
                """,
                
                # Hash indexes for exact lookups
                """
                CREATE INDEX IF NOT EXISTS idx_documents_content_hash 
                ON documents USING hash (content_hash)
                """,
                
                # Composite index for user ownership checks
                """
                CREATE INDEX IF NOT EXISTS idx_searchspaces_user_id 
                ON searchspaces (user_id)
                """,
                
                # Index for chunk-document relationships
                """
                CREATE INDEX IF NOT EXISTS idx_chunks_document_id 
                ON chunks (document_id)
                """,
            ]
            
            for i, index_sql in enumerate(indexes_to_create, 1):
                try:
                    print(f"   Creating index {i}/{len(indexes_to_create)}...")
                    await conn.execute(text(index_sql.strip()))
                    print(f"   ✅ Index {i} created successfully")
                except Exception as e:
                    print(f"   ⚠️  Index {i} creation warning: {e}")
            
            print("\n2. Creating additional performance indexes...")
            
            # Additional performance indexes
            performance_indexes = [
                # Partial indexes for active documents
                """
                CREATE INDEX IF NOT EXISTS idx_documents_active_recent 
                ON documents (created_at DESC) 
                WHERE created_at > (CURRENT_DATE - INTERVAL '90 days')
                """,
                
                # Covering index for common queries
                """
                CREATE INDEX IF NOT EXISTS idx_documents_covering 
                ON documents (search_space_id, document_type, created_at) 
                INCLUDE (title, content_hash)
                """,
                
                # Index for similarity threshold queries
                """
                CREATE INDEX IF NOT EXISTS idx_documents_embedding_distance 
                ON documents USING btree (
                    (embedding <=> '[0,0,0,0,0,0,0,0,0,0]'::vector)
                )
                """,
            ]
            
            for i, index_sql in enumerate(performance_indexes, 1):
                try:
                    print(f"   Creating performance index {i}/{len(performance_indexes)}...")
                    await conn.execute(text(index_sql.strip()))
                    print(f"   ✅ Performance index {i} created successfully")
                except Exception as e:
                    print(f"   ⚠️  Performance index {i} creation warning: {e}")
            
            print("\n3. Updating table statistics...")
            
            # Update statistics for better query planning
            stats_queries = [
                "ANALYZE documents",
                "ANALYZE chunks", 
                "ANALYZE searchspaces",
            ]
            
            for stats_query in stats_queries:
                try:
                    await conn.execute(text(stats_query))
                    print(f"   ✅ Updated statistics for {stats_query.split()[1]}")
                except Exception as e:
                    print(f"   ⚠️  Statistics update warning: {e}")
            
            print("\n4. Configuring PostgreSQL settings for search optimization...")
            
            # Recommended PostgreSQL settings for search performance
            pg_settings = [
                # Increase work memory for sorting and hashing
                "SET work_mem = '256MB'",
                
                # Increase maintenance work memory for index creation
                "SET maintenance_work_mem = '1GB'",
                
                # Enable parallel query execution
                "SET max_parallel_workers_per_gather = 4",
                
                # Optimize for search workloads
                "SET random_page_cost = 1.1",
                "SET seq_page_cost = 1.0",
            ]
            
            for setting in pg_settings:
                try:
                    await conn.execute(text(setting))
                    print(f"   ✅ Applied setting: {setting}")
                except Exception as e:
                    print(f"   ⚠️  Setting warning: {e}")
            
            print("\n5. Checking index usage and recommendations...")
            
            # Check existing indexes
            index_check_query = """
            SELECT 
                schemaname,
                tablename,
                indexname,
                idx_tup_read,
                idx_tup_fetch
            FROM pg_stat_user_indexes 
            WHERE schemaname = 'public' 
            AND tablename IN ('documents', 'chunks', 'searchspaces')
            ORDER BY idx_tup_read DESC
            """
            
            try:
                result = await conn.execute(text(index_check_query))
                indexes = result.fetchall()
                
                print(f"   Found {len(indexes)} indexes on search tables:")
                for idx in indexes[:10]:  # Show top 10
                    print(f"      {idx.tablename}.{idx.indexname}: {idx.idx_tup_read} reads")
                    
            except Exception as e:
                print(f"   ⚠️  Index check warning: {e}")
            
            print("\n" + "=" * 60)
            print("✅ Database optimization completed!")
            print("\n📊 Recommendations:")
            print("   1. Monitor query performance with EXPLAIN ANALYZE")
            print("   2. Consider partitioning for very large document tables")
            print("   3. Regular VACUUM and ANALYZE for optimal performance")
            print("   4. Monitor index usage with pg_stat_user_indexes")
            print("   5. Adjust PostgreSQL configuration based on workload")
            
    except Exception as e:
        print(f"❌ Error during optimization: {e}")
        import traceback
        traceback.print_exc()

async def check_search_performance():
    """Check current search performance metrics."""
    
    print("\n🔍 Checking Search Performance Metrics")
    print("=" * 50)
    
    try:
        from app.db import engine
        from sqlalchemy import text
        
        async with engine.begin() as conn:
            # Check table sizes
            size_query = """
            SELECT 
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                pg_total_relation_size(schemaname||'.'||tablename) as bytes
            FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename IN ('documents', 'chunks', 'searchspaces')
            ORDER BY bytes DESC
            """
            
            result = await conn.execute(text(size_query))
            tables = result.fetchall()
            
            print("📏 Table Sizes:")
            for table in tables:
                print(f"   {table.tablename}: {table.size}")
            
            # Check index sizes
            index_size_query = """
            SELECT 
                indexname,
                pg_size_pretty(pg_relation_size(indexname::regclass)) as size
            FROM pg_indexes 
            WHERE schemaname = 'public' 
            AND tablename IN ('documents', 'chunks', 'searchspaces')
            ORDER BY pg_relation_size(indexname::regclass) DESC
            LIMIT 10
            """
            
            result = await conn.execute(text(index_size_query))
            indexes = result.fetchall()
            
            print("\n📊 Largest Indexes:")
            for idx in indexes:
                print(f"   {idx.indexname}: {idx.size}")
                
    except Exception as e:
        print(f"❌ Error checking performance: {e}")

if __name__ == "__main__":
    print("Starting database optimization for enhanced search...")
    asyncio.run(optimize_search_indexes())
    asyncio.run(check_search_performance())
